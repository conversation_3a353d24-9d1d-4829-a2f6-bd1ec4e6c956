{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 1,
   "id": "f3c6c252",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "application/vnd.microsoft.datawrangler.viewer.v0+json": {
       "columns": [
        {
         "name": "index",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "CustomerKey",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "OrderDate",
         "rawType": "datetime64[ns]",
         "type": "datetime"
        },
        {
         "name": "SalesAmount",
         "rawType": "float64",
         "type": "float"
        }
       ],
       "ref": "72e52760-7213-4fbd-9920-cef1d90687c7",
       "rows": [
        [
         "0",
         "21768",
         "2010-12-29 00:00:00",
         "3578.27"
        ],
        [
         "1",
         "28389",
         "2010-12-29 00:00:00",
         "3399.99"
        ],
        [
         "2",
         "25863",
         "2010-12-29 00:00:00",
         "3399.99"
        ],
        [
         "3",
         "14501",
         "2010-12-29 00:00:00",
         "699.0982"
        ],
        [
         "4",
         "11003",
         "2010-12-29 00:00:00",
         "3399.99"
        ]
       ],
       "shape": {
        "columns": 3,
        "rows": 5
       }
      },
      "text/html": [
       "<div>\n",
       "<style scoped>\n",
       "    .dataframe tbody tr th:only-of-type {\n",
       "        vertical-align: middle;\n",
       "    }\n",
       "\n",
       "    .dataframe tbody tr th {\n",
       "        vertical-align: top;\n",
       "    }\n",
       "\n",
       "    .dataframe thead th {\n",
       "        text-align: right;\n",
       "    }\n",
       "</style>\n",
       "<table border=\"1\" class=\"dataframe\">\n",
       "  <thead>\n",
       "    <tr style=\"text-align: right;\">\n",
       "      <th></th>\n",
       "      <th>CustomerKey</th>\n",
       "      <th>OrderDate</th>\n",
       "      <th>SalesAmount</th>\n",
       "    </tr>\n",
       "  </thead>\n",
       "  <tbody>\n",
       "    <tr>\n",
       "      <th>0</th>\n",
       "      <td>21768</td>\n",
       "      <td>2010-12-29</td>\n",
       "      <td>3578.2700</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>1</th>\n",
       "      <td>28389</td>\n",
       "      <td>2010-12-29</td>\n",
       "      <td>3399.9900</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>2</th>\n",
       "      <td>25863</td>\n",
       "      <td>2010-12-29</td>\n",
       "      <td>3399.9900</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>3</th>\n",
       "      <td>14501</td>\n",
       "      <td>2010-12-29</td>\n",
       "      <td>699.0982</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>4</th>\n",
       "      <td>11003</td>\n",
       "      <td>2010-12-29</td>\n",
       "      <td>3399.9900</td>\n",
       "    </tr>\n",
       "  </tbody>\n",
       "</table>\n",
       "</div>"
      ],
      "text/plain": [
       "   CustomerKey  OrderDate  SalesAmount\n",
       "0        21768 2010-12-29    3578.2700\n",
       "1        28389 2010-12-29    3399.9900\n",
       "2        25863 2010-12-29    3399.9900\n",
       "3        14501 2010-12-29     699.0982\n",
       "4        11003 2010-12-29    3399.9900"
      ]
     },
     "execution_count": 1,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "import pandas as pd\n",
    "from sqlalchemy import create_engine, text\n",
    "import urllib\n",
    "\n",
    "# 1. Define connection string\n",
    "connection_string = (\n",
    "    \"Driver={SQL Server};\"\n",
    "    \"Server=LAPTOP-E0RGI0HO\\\\MSSQLSERVER1;\"\n",
    "    \"Database=AdventureWorksDW2022;\"\n",
    "    \"Trusted_Connection=yes;\"\n",
    ")\n",
    "\n",
    "# 2. URL-encode the connection string\n",
    "connection_uri = f\"mssql+pyodbc:///?odbc_connect={urllib.parse.quote_plus(connection_string)}\"\n",
    "\n",
    "# 3. Create the SQLAlchemy engine (no 'future' flag needed)\n",
    "engine = create_engine(connection_uri)\n",
    "\n",
    "# 4. Run the query\n",
    "query = text(\"SELECT CustomerKey, OrderDate, SalesAmount FROM FactInternetSales\")\n",
    "with engine.connect() as conn:\n",
    "    df = pd.read_sql(query, conn)\n",
    "\n",
    "# 5. Process\n",
    "df['OrderDate'] = pd.to_datetime(df['OrderDate'])\n",
    "df.head()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "id": "923a3101",
   "metadata": {},
   "outputs": [],
   "source": [
    "df_monthly = df.groupby(pd.Grouper(key='OrderDate', freq='M'))['SalesAmount'].sum().reset_index()\n",
    "df_monthly.columns = ['ds', 'y']  # Prophet expects these column names\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,
   "id": "43aab2b1",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1200x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import numpy as np\n",
    "\n",
    "# Simple moving average forecast\n",
    "def simple_forecast(df, periods=6, window=3):\n",
    "    # Calculate moving average for the last 'window' periods\n",
    "    recent_avg = df['y'].tail(window).mean()\n",
    "    \n",
    "    # Create forecast dates\n",
    "    last_date = df['ds'].max()\n",
    "    future_dates = pd.date_range(start=last_date + pd.DateOffset(months=1), \n",
    "                                periods=periods, freq='M')\n",
    "    \n",
    "    # Simple forecast (just repeat the recent average)\n",
    "    forecast_values = [recent_avg] * periods\n",
    "    \n",
    "    return future_dates, forecast_values\n",
    "\n",
    "# Assuming your dataframe has 'ds' and 'y' columns\n",
    "future_dates, forecast_values = simple_forecast(df_monthly)\n",
    "\n",
    "# Plot\n",
    "plt.figure(figsize=(12, 6))\n",
    "plt.plot(df_monthly['ds'], df_monthly['y'], 'o-', label='Historical Data')\n",
    "plt.plot(future_dates, forecast_values, 'o--', color='red', label='Forecast')\n",
    "plt.title(\"Sales Forecast (Simple Moving Average)\")\n",
    "plt.xlabel(\"Date\")\n",
    "plt.ylabel(\"Sales\")\n",
    "plt.legend()\n",
    "plt.grid(True, alpha=0.3)\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "id": "a3a9d638",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Collecting statsmodels\n",
      "  Downloading statsmodels-0.14.5-cp310-cp310-win_amd64.whl (9.6 MB)\n",
      "     ---------------------------------------- 9.6/9.6 MB 2.0 MB/s eta 0:00:00\n",
      "Collecting prophet\n",
      "  Downloading prophet-1.1.7-py3-none-win_amd64.whl (13.3 MB)\n",
      "     ---------------------------------------- 13.3/13.3 MB 2.0 MB/s eta 0:00:00\n",
      "Requirement already satisfied: numpy<3,>=1.22.3 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from statsmodels) (1.24.1)\n",
      "Requirement already satisfied: scipy!=1.9.2,>=1.8 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from statsmodels) (1.10.0)\n",
      "Requirement already satisfied: packaging>=21.3 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from statsmodels) (22.0)\n",
      "Requirement already satisfied: pandas!=2.1.0,>=1.4 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from statsmodels) (1.5.3)\n",
      "Collecting patsy>=0.5.6\n",
      "  Downloading patsy-1.0.1-py2.py3-none-any.whl (232 kB)\n",
      "     -------------------------------------- 232.9/232.9 kB 2.4 MB/s eta 0:00:00\n",
      "Collecting importlib_resources\n",
      "  Downloading importlib_resources-6.5.2-py3-none-any.whl (37 kB)\n",
      "Collecting cmdstanpy>=1.0.4\n",
      "  Using cached cmdstanpy-1.2.5-py3-none-any.whl (94 kB)\n",
      "Requirement already satisfied: matplotlib>=2.0.0 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from prophet) (3.6.3)\n",
      "Collecting holidays<1,>=0.25\n",
      "  Downloading holidays-0.77-py3-none-any.whl (1.2 MB)\n",
      "     ---------------------------------------- 1.2/1.2 MB 2.0 MB/s eta 0:00:00\n",
      "Requirement already satisfied: tqdm>=4.36.1 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from prophet) (4.64.1)\n",
      "Collecting stanio<2.0.0,>=0.4.0\n",
      "  Downloading stanio-0.5.1-py3-none-any.whl (8.1 kB)\n",
      "Requirement already satisfied: python-dateutil in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from holidays<1,>=0.25->prophet) (2.8.2)\n",
      "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (0.11.0)\n",
      "Requirement already satisfied: pyparsing>=2.2.1 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (3.0.9)\n",
      "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (1.0.7)\n",
      "Requirement already satisfied: kiwisolver>=1.0.1 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (1.4.4)\n",
      "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (4.38.0)\n",
      "Requirement already satisfied: pillow>=6.2.0 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from matplotlib>=2.0.0->prophet) (9.4.0)\n",
      "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from pandas!=2.1.0,>=1.4->statsmodels) (2022.7.1)\n",
      "Requirement already satisfied: colorama in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from tqdm>=4.36.1->prophet) (0.4.6)\n",
      "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from python-dateutil->holidays<1,>=0.25->prophet) (1.16.0)\n",
      "Installing collected packages: stanio, patsy, importlib_resources, holidays, statsmodels, cmdstanpy, prophet\n",
      "Successfully installed cmdstanpy-1.2.5 holidays-0.77 importlib_resources-6.5.2 patsy-1.0.1 prophet-1.1.7 stanio-0.5.1 statsmodels-0.14.5\n",
      "Note: you may need to restart the kernel to use updated packages.\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "  WARNING: The scripts install_cmdstan.exe and install_cxx_toolchain.exe are installed in 'c:\\Users\\<USER>\\.conda\\envs\\py\\Scripts' which is not on PATH.\n",
      "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\n"
     ]
    }
   ],
   "source": [
    "pip install statsmodels prophet"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 7,
   "id": "3a358154",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1000x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "from statsmodels.tsa.holtwinters import ExponentialSmoothing\n",
    "import matplotlib.pyplot as plt\n",
    "\n",
    "# Assuming your data has 'ds' and 'y' columns like Prophet expects\n",
    "model = ExponentialSmoothing(df_monthly['y'], trend='add', seasonal='add', seasonal_periods=12)\n",
    "fitted_model = model.fit()\n",
    "forecast = fitted_model.forecast(steps=6)\n",
    "\n",
    "plt.figure(figsize=(10, 6))\n",
    "plt.plot(df_monthly.index, df_monthly['y'], label='Historical')\n",
    "plt.plot(range(len(df_monthly), len(df_monthly) + 6), forecast, label='Forecast')\n",
    "plt.title(\"Sales Forecast\")\n",
    "plt.legend()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 8,
   "id": "89c3cb6f",
   "metadata": {},
   "outputs": [
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "c:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\tqdm\\auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n",
      "  from .autonotebook import tqdm as notebook_tqdm\n",
      "Importing plotly failed. Interactive plots will not work.\n",
      "22:19:55 - cmdstanpy - INFO - Chain [1] start processing\n",
      "22:19:55 - cmdstanpy - INFO - Chain [1] done processing\n"
     ]
    },
    {
     "data": {
      "text/plain": [
       "Text(0.5, 1.0, 'Sales Forecast')"
      ]
     },
     "execution_count": 8,
     "metadata": {},
     "output_type": "execute_result"
    },
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1000x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "from prophet import Prophet\n",
    "import matplotlib.pyplot as plt\n",
    "model = Prophet()\n",
    "model.fit(df_monthly)\n",
    "future = model.make_future_dataframe(periods=6, freq='M')\n",
    "forecast = model.predict(future)\n",
    "fig = model.plot(forecast)\n",
    "plt.title(\"Sales Forecast\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "id": "9cf56c83",
   "metadata": {},
   "outputs": [],
   "source": [
    "snapshot_date = df['OrderDate'].max() + pd.Timedelta(days=1)\n",
    "\n",
    "rfm = df.groupby('CustomerKey').agg({\n",
    "    'OrderDate': lambda x: (snapshot_date - x.max()).days,\n",
    "    'CustomerKey': 'count',\n",
    "    'SalesAmount': 'sum'\n",
    "})\n",
    "\n",
    "rfm.columns = ['Recency', 'Frequency', 'Monetary']\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 13,
   "id": "fe071d1f",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Collecting scikit-learn\n",
      "  Downloading scikit_learn-1.7.1-cp310-cp310-win_amd64.whl (8.9 MB)\n",
      "     ---------------------------------------- 8.9/8.9 MB 2.1 MB/s eta 0:00:00\n",
      "Requirement already satisfied: scipy>=1.8.0 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from scikit-learn) (1.10.0)\n",
      "Collecting threadpoolctl>=3.1.0\n",
      "  Downloading threadpoolctl-3.6.0-py3-none-any.whl (18 kB)\n",
      "Requirement already satisfied: numpy>=1.22.0 in c:\\users\\<USER>\\.conda\\envs\\py\\lib\\site-packages (from scikit-learn) (1.24.1)\n",
      "Collecting joblib>=1.2.0\n",
      "  Downloading joblib-1.5.1-py3-none-any.whl (307 kB)\n",
      "     -------------------------------------- 307.7/307.7 kB 2.1 MB/s eta 0:00:00\n",
      "Installing collected packages: threadpoolctl, joblib, scikit-learn\n",
      "Successfully installed joblib-1.5.1 scikit-learn-1.7.1 threadpoolctl-3.6.0\n",
      "Note: you may need to restart the kernel to use updated packages.\n"
     ]
    }
   ],
   "source": [
    "pip install scikit-learn"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 15,
   "id": "ed4241a2",
   "metadata": {},
   "outputs": [],
   "source": [
    "from sklearn.preprocessing import StandardScaler\n",
    "from sklearn.cluster import KMeans\n",
    "\n",
    "scaler = StandardScaler()\n",
    "rfm_scaled = scaler.fit_transform(rfm)\n",
    "\n",
    "kmeans = KMeans(n_clusters=4, random_state=42)\n",
    "rfm['Cluster'] = kmeans.fit_predict(rfm_scaled)\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 16,
   "id": "fc16d960",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 640x480 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "import seaborn as sns\n",
    "\n",
    "sns.scatterplot(data=rfm, x='Recency', y='Monetary', hue='Cluster', palette='Set2')\n",
    "plt.title(\"Customer Segmentation\")\n",
    "plt.show()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 17,
   "id": "08eb767b",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "application/vnd.microsoft.datawrangler.viewer.v0+json": {
       "columns": [
        {
         "name": "Cluster",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "Recency",
         "rawType": "float64",
         "type": "float"
        },
        {
         "name": "Frequency",
         "rawType": "float64",
         "type": "float"
        },
        {
         "name": "Monetary",
         "rawType": "float64",
         "type": "float"
        }
       ],
       "ref": "7bf3586c-efcb-4a27-96db-d8a2cfa55568",
       "rows": [
        [
         "0",
         "172.50670177118238",
         "4.7056007659167065",
         "4946.42855098133"
        ],
        [
         "1",
         "176.48473915866947",
         "2.7994083267190994",
         "537.847271332708"
        ],
        [
         "2",
         "18.514285714285716",
         "43.68571428571428",
         "985.7434285714285"
        ],
        [
         "3",
         "851.5097087378641",
         "1.0",
         "2922.263061407767"
        ]
       ],
       "shape": {
        "columns": 3,
        "rows": 4
       }
      },
      "text/html": [
       "<div>\n",
       "<style scoped>\n",
       "    .dataframe tbody tr th:only-of-type {\n",
       "        vertical-align: middle;\n",
       "    }\n",
       "\n",
       "    .dataframe tbody tr th {\n",
       "        vertical-align: top;\n",
       "    }\n",
       "\n",
       "    .dataframe thead th {\n",
       "        text-align: right;\n",
       "    }\n",
       "</style>\n",
       "<table border=\"1\" class=\"dataframe\">\n",
       "  <thead>\n",
       "    <tr style=\"text-align: right;\">\n",
       "      <th></th>\n",
       "      <th>Recency</th>\n",
       "      <th>Frequency</th>\n",
       "      <th>Monetary</th>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>Cluster</th>\n",
       "      <th></th>\n",
       "      <th></th>\n",
       "      <th></th>\n",
       "    </tr>\n",
       "  </thead>\n",
       "  <tbody>\n",
       "    <tr>\n",
       "      <th>0</th>\n",
       "      <td>172.506702</td>\n",
       "      <td>4.705601</td>\n",
       "      <td>4946.428551</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>1</th>\n",
       "      <td>176.484739</td>\n",
       "      <td>2.799408</td>\n",
       "      <td>537.847271</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>2</th>\n",
       "      <td>18.514286</td>\n",
       "      <td>43.685714</td>\n",
       "      <td>985.743429</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>3</th>\n",
       "      <td>851.509709</td>\n",
       "      <td>1.000000</td>\n",
       "      <td>2922.263061</td>\n",
       "    </tr>\n",
       "  </tbody>\n",
       "</table>\n",
       "</div>"
      ],
      "text/plain": [
       "            Recency  Frequency     Monetary\n",
       "Cluster                                    \n",
       "0        172.506702   4.705601  4946.428551\n",
       "1        176.484739   2.799408   537.847271\n",
       "2         18.514286  43.685714   985.743429\n",
       "3        851.509709   1.000000  2922.263061"
      ]
     },
     "execution_count": 17,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "rfm.groupby('Cluster').mean()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 19,
   "id": "652343f2",
   "metadata": {},
   "outputs": [
    {
     "ename": "ObjectNotExecutableError",
     "evalue": "Not an executable object: '\\nSELECT ProductKey, CustomerKey, OrderDate, OrderQuantity, SalesAmount\\nFROM FactInternetSales\\n'",
     "output_type": "error",
     "traceback": [
      "\u001b[1;31m---------------------------------------------------------------------------\u001b[0m",
      "\u001b[1;31mAttributeError\u001b[0m                            Traceback (most recent call last)",
      "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\sqlalchemy\\engine\\base.py:1409\u001b[0m, in \u001b[0;36mConnection.execute\u001b[1;34m(self, statement, parameters, execution_options)\u001b[0m\n\u001b[0;32m   1408\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1409\u001b[0m     meth \u001b[38;5;241m=\u001b[39m \u001b[43mstatement\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_on_connection\u001b[49m\n\u001b[0;32m   1410\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n",
      "\u001b[1;31mAttributeError\u001b[0m: 'str' object has no attribute '_execute_on_connection'",
      "\nThe above exception was the direct cause of the following exception:\n",
      "\u001b[1;31mObjectNotExecutableError\u001b[0m                  Traceback (most recent call last)",
      "Cell \u001b[1;32mIn[19], line 6\u001b[0m\n\u001b[0;32m      1\u001b[0m query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;124mSELECT ProductKey, CustomerKey, OrderDate, OrderQuantity, SalesAmount\u001b[39m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;124mFROM FactInternetSales\u001b[39m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m engine\u001b[38;5;241m.\u001b[39mconnect() \u001b[38;5;28;01mas\u001b[39;00m conn:\n\u001b[1;32m----> 6\u001b[0m     df \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_sql\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      7\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOrderDate\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOrderDate\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[0;32m      8\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOrderMonth\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOrderDate\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mdt\u001b[38;5;241m.\u001b[39mto_period(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mM\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mstr\u001b[39m)\n",
      "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\pandas\\io\\sql.py:590\u001b[0m, in \u001b[0;36mread_sql\u001b[1;34m(sql, con, index_col, coerce_float, params, parse_dates, columns, chunksize)\u001b[0m\n\u001b[0;32m    581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m pandas_sql\u001b[38;5;241m.\u001b[39mread_table(\n\u001b[0;32m    582\u001b[0m         sql,\n\u001b[0;32m    583\u001b[0m         index_col\u001b[38;5;241m=\u001b[39mindex_col,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    587\u001b[0m         chunksize\u001b[38;5;241m=\u001b[39mchunksize,\n\u001b[0;32m    588\u001b[0m     )\n\u001b[0;32m    589\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 590\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpandas_sql\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_query\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    591\u001b[0m \u001b[43m        \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    592\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex_col\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    593\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    594\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcoerce_float\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcoerce_float\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    595\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparse_dates\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_dates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    596\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    597\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n",
      "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\pandas\\io\\sql.py:1560\u001b[0m, in \u001b[0;36mSQLDatabase.read_query\u001b[1;34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize, dtype)\u001b[0m\n\u001b[0;32m   1512\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   1513\u001b[0m \u001b[38;5;124;03mRead SQL query into a DataFrame.\u001b[39;00m\n\u001b[0;32m   1514\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1556\u001b[0m \n\u001b[0;32m   1557\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   1558\u001b[0m args \u001b[38;5;241m=\u001b[39m _convert_params(sql, params)\n\u001b[1;32m-> 1560\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1561\u001b[0m columns \u001b[38;5;241m=\u001b[39m result\u001b[38;5;241m.\u001b[39mkeys()\n\u001b[0;32m   1563\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n",
      "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\pandas\\io\\sql.py:1405\u001b[0m, in \u001b[0;36mSQLDatabase.execute\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1403\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mexecute\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m   1404\u001b[0m     \u001b[38;5;124;03m\"\"\"Simple passthrough to SQLAlchemy connectable\"\"\"\u001b[39;00m\n\u001b[1;32m-> 1405\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnectable\u001b[38;5;241m.\u001b[39mexecution_options()\u001b[38;5;241m.\u001b[39mexecute(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n",
      "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\py\\lib\\site-packages\\sqlalchemy\\engine\\base.py:1411\u001b[0m, in \u001b[0;36mConnection.execute\u001b[1;34m(self, statement, parameters, execution_options)\u001b[0m\n\u001b[0;32m   1409\u001b[0m     meth \u001b[38;5;241m=\u001b[39m statement\u001b[38;5;241m.\u001b[39m_execute_on_connection\n\u001b[0;32m   1410\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m-> 1411\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mObjectNotExecutableError(statement) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   1412\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   1413\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m meth(\n\u001b[0;32m   1414\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   1415\u001b[0m         distilled_parameters,\n\u001b[0;32m   1416\u001b[0m         execution_options \u001b[38;5;129;01mor\u001b[39;00m NO_OPTIONS,\n\u001b[0;32m   1417\u001b[0m     )\n",
      "\u001b[1;31mObjectNotExecutableError\u001b[0m: Not an executable object: '\\nSELECT ProductKey, CustomerKey, OrderDate, OrderQuantity, SalesAmount\\nFROM FactInternetSales\\n'"
     ]
    }
   ],
   "source": [
    "query = \"\"\"\n",
    "SELECT ProductKey, CustomerKey, OrderDate, OrderQuantity, SalesAmount\n",
    "FROM FactInternetSales\n",
    "\"\"\"\n",
    "with engine.connect() as conn:\n",
    "    df = pd.read_sql(query, conn)\n",
    "df['OrderDate'] = pd.to_datetime(df['OrderDate'])\n",
    "df['OrderMonth'] = df['OrderDate'].dt.to_period('M').astype(str)\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "c69dced4",
   "metadata": {},
   "outputs": [],
   "source": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "py",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.10.9"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
  },
  {
   "cell_type": "markdown",
   "id": "data_exploration",
   "metadata": {},
   "source": [
    "## Data Exploration and Feature Engineering\n",
    "Let's explore the data and create additional features for our prediction model"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "data_exploration_code",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Get more detailed data from FactInternetSales with related dimensions\n",
    "detailed_query = text(\"\"\"\n",
    "SELECT \n",
    "    fis.CustomerKey,\n",
    "    fis.ProductKey,\n",
    "    fis.OrderDateKey,\n",
    "    fis.OrderDate,\n",
    "    fis.SalesAmount,\n",
    "    fis.OrderQuantity,\n",
    "    fis.UnitPrice,\n",
    "    fis.TotalProductCost,\n",
    "    dp.ProductSubcategoryKey,\n",
    "    dp.ProductCategoryKey,\n",
    "    dc.GeographyKey,\n",
    "    dc.YearlyIncome,\n",
    "    dc.BirthDate,\n",
    "    dc.MaritalStatus,\n",
    "    dc.Gender,\n",
    "    dc.Education,\n",
    "    dc.Occupation,\n",
    "    dg.CountryRegionCode,\n",
    "    dg.StateProvinceCode\n",
    "FROM FactInternetSales fis\n",
    "LEFT JOIN DimProduct dp ON fis.ProductKey = dp.ProductKey\n",
    "LEFT JOIN DimCustomer dc ON fis.CustomerKey = dc.CustomerKey\n",
    "LEFT JOIN DimGeography dg ON dc.GeographyKey = dg.GeographyKey\n",
    "WHERE fis.OrderDate >= '2011-01-01'\n",
    "\"\"\")\n",
    "\n",
    "with engine.connect() as conn:\n",
    "    df_detailed = pd.read_sql(detailed_query, conn)\n",
    "\n",
    "print(f\"Dataset shape: {df_detailed.shape}\")\n",
    "print(\"\\nFirst few rows:\")\n",
    "df_detailed.head()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "feature_engineering",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature Engineering\n",
    "import numpy as np\n",
    "from datetime import datetime\n",
    "\n",
    "# Convert date columns\n",
    "df_detailed['OrderDate'] = pd.to_datetime(df_detailed['OrderDate'])\n",
    "df_detailed['BirthDate'] = pd.to_datetime(df_detailed['BirthDate'])\n",
    "\n",
    "# Create time-based features\n",
    "df_detailed['Year'] = df_detailed['OrderDate'].dt.year\n",
    "df_detailed['Month'] = df_detailed['OrderDate'].dt.month\n",
    "df_detailed['Quarter'] = df_detailed['OrderDate'].dt.quarter\n",
    "df_detailed['DayOfWeek'] = df_detailed['OrderDate'].dt.dayofweek\n",
    "df_detailed['IsWeekend'] = df_detailed['DayOfWeek'].isin([5, 6]).astype(int)\n",
    "\n",
    "# Calculate customer age at time of purchase\n",
    "df_detailed['CustomerAge'] = (df_detailed['OrderDate'] - df_detailed['BirthDate']).dt.days / 365.25\n",
    "\n",
    "# Create profit margin\n",
    "df_detailed['ProfitMargin'] = (df_detailed['SalesAmount'] - df_detailed['TotalProductCost']) / df_detailed['SalesAmount']\n",
    "\n",
    "# Create customer segments based on yearly income\n",
    "df_detailed['IncomeSegment'] = pd.cut(df_detailed['YearlyIncome'], \n",
    "                                     bins=[0, 25000, 50000, 75000, 100000, float('inf')],\n",
    "                                     labels=['Low', 'Lower-Mid', 'Mid', 'Upper-Mid', 'High'])\n",
    "\n",
    "print(\"Feature engineering completed!\")\n",
    "print(f\"New dataset shape: {df_detailed.shape}\")\n",
    "df_detailed.info()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "eda_section",
   "metadata": {},
   "source": [
    "## Exploratory Data Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "eda_code",
   "metadata": {},
   "outputs": [],
   "source": [
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "\n",
    "# Set style\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Create subplots for EDA\n",
    "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "\n",
    "# Sales by Year\n",
    "yearly_sales = df_detailed.groupby('Year')['SalesAmount'].sum()\n",
    "axes[0,0].plot(yearly_sales.index, yearly_sales.values, marker='o', linewidth=2)\n",
    "axes[0,0].set_title('Total Sales by Year')\n",
    "axes[0,0].set_xlabel('Year')\n",
    "axes[0,0].set_ylabel('Sales Amount')\n",
    "axes[0,0].grid(True, alpha=0.3)\n",
    "\n",
    "# Sales by Quarter\n",
    "quarterly_sales = df_detailed.groupby('Quarter')['SalesAmount'].sum()\n",
    "axes[0,1].bar(quarterly_sales.index, quarterly_sales.values)\n",
    "axes[0,1].set_title('Sales by Quarter')\n",
    "axes[0,1].set_xlabel('Quarter')\n",
    "axes[0,1].set_ylabel('Sales Amount')\n",
    "\n",
    "# Sales by Income Segment\n",
    "income_sales = df_detailed.groupby('IncomeSegment')['SalesAmount'].sum()\n",
    "axes[0,2].bar(range(len(income_sales)), income_sales.values)\n",
    "axes[0,2].set_title('Sales by Income Segment')\n",
    "axes[0,2].set_xlabel('Income Segment')\n",
    "axes[0,2].set_ylabel('Sales Amount')\n",
    "axes[0,2].set_xticks(range(len(income_sales)))\n",
    "axes[0,2].set_xticklabels(income_sales.index, rotation=45)\n",
    "\n",
    "# Customer Age Distribution\n",
    "axes[1,0].hist(df_detailed['CustomerAge'].dropna(), bins=30, alpha=0.7)\n",
    "axes[1,0].set_title('Customer Age Distribution')\n",
    "axes[1,0].set_xlabel('Age')\n",
    "axes[1,0].set_ylabel('Frequency')\n",
    "\n",
    "# Sales Amount Distribution\n",
    "axes[1,1].hist(df_detailed['SalesAmount'], bins=50, alpha=0.7)\n",
    "axes[1,1].set_title('Sales Amount Distribution')\n",
    "axes[1,1].set_xlabel('Sales Amount')\n",
    "axes[1,1].set_ylabel('Frequency')\n",
    "\n",
    "# Profit Margin Distribution\n",
    "axes[1,2].hist(df_detailed['ProfitMargin'].dropna(), bins=30, alpha=0.7)\n",
    "axes[1,2].set_title('Profit Margin Distribution')\n",
    "axes[1,2].set_xlabel('Profit Margin')\n",
    "axes[1,2].set_ylabel('Frequency')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Summary statistics\n",
    "print(\"\\nSummary Statistics:\")\n",
    "print(df_detailed[['SalesAmount', 'OrderQuantity', 'CustomerAge', 'ProfitMargin']].describe())"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "model_section",
   "metadata": {},
   "source": [
    "## Prediction Models\n",
    "We'll build multiple prediction models:\n",
    "1. Time Series Forecasting (Prophet) - for sales forecasting\n",
    "2. Customer Purchase Prediction (Classification)\n",
    "3. Sales Amount Prediction (Regression)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "time_series_model",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 1. Time Series Forecasting with Prophet\n",
    "from prophet import Prophet\n",
    "from sklearn.metrics import mean_absolute_error, mean_squared_error\n",
    "\n",
    "# Prepare data for Prophet (already done in cell 2)\n",
    "print(\"Time Series Data Shape:\", df_monthly.shape)\n",
    "print(df_monthly.head())\n",
    "\n",
    "# Split data for validation\n",
    "train_size = int(len(df_monthly) * 0.8)\n",
    "train_ts = df_monthly[:train_size]\n",
    "test_ts = df_monthly[train_size:]\n",
    "\n",
    "# Create and fit Prophet model\n",
    "prophet_model = Prophet(\n",
    "    yearly_seasonality=True,\n",
    "    weekly_seasonality=False,\n",
    "    daily_seasonality=False,\n",
    "    seasonality_mode='multiplicative'\n",
    ")\n",
    "\n",
    "prophet_model.fit(train_ts)\n",
    "\n",
    "# Make predictions\n",
    "future = prophet_model.make_future_dataframe(periods=len(test_ts), freq='M')\n",
    "forecast = prophet_model.predict(future)\n",
    "\n",
    "# Evaluate model\n",
    "train_pred = forecast[:len(train_ts)]['yhat']\n",
    "test_pred = forecast[len(train_ts):]['yhat']\n",
    "\n",
    "print(f\"\\nTime Series Model Performance:\")\n",
    "print(f\"Train MAE: {mean_absolute_error(train_ts['y'], train_pred):,.2f}\")\n",
    "print(f\"Test MAE: {mean_absolute_error(test_ts['y'], test_pred):,.2f}\")\n",
    "print(f\"Train RMSE: {np.sqrt(mean_squared_error(train_ts['y'], train_pred)):,.2f}\")\n",
    "print(f\"Test RMSE: {np.sqrt(mean_squared_error(test_ts['y'], test_pred)):,.2f}\")\n",
    "\n",
    "# Plot results\n",
    "fig, ax = plt.subplots(figsize=(15, 8))\n",
    "prophet_model.plot(forecast, ax=ax)\n",
    "ax.set_title('Sales Forecasting with Prophet')\n",
    "ax.set_ylabel('Sales Amount')\n",
    "plt.show()\n",
    "\n",
    "# Plot components\n",
    "fig = prophet_model.plot_components(forecast)\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "classification_model",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 2. Customer Purchase Prediction (Classification)\n",
    "from sklearn.model_selection import train_test_split\n",
    "from sklearn.ensemble import RandomForestClassifier\n",
    "from sklearn.preprocessing import LabelEncoder\n",
    "from sklearn.metrics import classification_report, confusion_matrix\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Create target variable: High-value customer (above median sales)\n",
    "customer_summary = df_detailed.groupby('CustomerKey').agg({\n",
    "    'SalesAmount': ['sum', 'count', 'mean'],\n",
    "    'OrderQuantity': 'sum',\n",
    "    'CustomerAge': 'first',\n",
    "    'YearlyIncome': 'first',\n",
    "    'Gender': 'first',\n",
    "    'MaritalStatus': 'first',\n",
    "    'Education': 'first',\n",
    "    'CountryRegionCode': 'first'\n",
    "}).reset_index()\n",
    "\n",
    "# Flatten column names\n",
    "customer_summary.columns = ['CustomerKey', 'TotalSales', 'OrderCount', 'AvgOrderValue', \n",
    "                           'TotalQuantity', 'CustomerAge', 'YearlyIncome', 'Gender', \n",
    "                           'MaritalStatus', 'Education', 'CountryRegionCode']\n",
    "\n",
    "# Create target: High-value customer (above 75th percentile)\n",
    "threshold = customer_summary['TotalSales'].quantile(0.75)\n",
    "customer_summary['HighValueCustomer'] = (customer_summary['TotalSales'] > threshold).astype(int)\n",
    "\n",
    "# Prepare features for classification\n",
    "features_for_classification = ['CustomerAge', 'YearlyIncome', 'Gender', 'MaritalStatus', 'Education', 'CountryRegionCode']\n",
    "X_class = customer_summary[features_for_classification].copy()\n",
    "\n",
    "# Encode categorical variables\n",
    "label_encoders = {}\n",
    "for col in ['Gender', 'MaritalStatus', 'Education', 'CountryRegionCode']:\n",
    "    le = LabelEncoder()\n",
    "    X_class[col] = le.fit_transform(X_class[col].fillna('Unknown'))\n",
    "    label_encoders[col] = le\n",
    "\n",
    "# Handle missing values\n",
    "X_class = X_class.fillna(X_class.median())\n",
    "y_class = customer_summary['HighValueCustomer']\n",
    "\n",
    "# Split data\n",
    "X_train_class, X_test_class, y_train_class, y_test_class = train_test_split(\n",
    "    X_class, y_class, test_size=0.2, random_state=42, stratify=y_class\n",
    ")\n",
    "\n",
    "# Train Random Forest Classifier\n",
    "rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42)\n",
    "rf_classifier.fit(X_train_class, y_train_class)\n",
    "\n",
    "# Make predictions\n",
    "y_pred_class = rf_classifier.predict(X_test_class)\n",
    "\n",
    "# Evaluate classification model\n",
    "print(\"\\nCustomer Classification Model Performance:\")\n",
    "print(classification_report(y_test_class, y_pred_class))\n",
    "\n",
    "# Feature importance\n",
    "feature_importance = pd.DataFrame({\n",
    "    'feature': features_for_classification,\n",
    "    'importance': rf_classifier.feature_importances_\n",
    "}).sort_values('importance', ascending=False)\n",
    "\n",
    "print(\"\\nFeature Importance:\")\n",
    "print(feature_importance)\n",
    "\n",
    "# Plot feature importance\n",
    "plt.figure(figsize=(10, 6))\n",
    "sns.barplot(data=feature_importance, x='importance', y='feature')\n",
    "plt.title('Feature Importance for Customer Classification')\n",
    "plt.xlabel('Importance')\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "regression_model",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 3. Sales Amount Prediction (Regression)\n",
    "from sklearn.ensemble import RandomForestRegressor\n",
    "from sklearn.preprocessing import StandardScaler\n",
    "from sklearn.metrics import r2_score, mean_absolute_percentage_error\n",
    "\n",
    "# Prepare features for regression\n",
    "features_for_regression = ['ProductKey', 'CustomerAge', 'YearlyIncome', 'OrderQuantity', \n",
    "                          'Month', 'Quarter', 'DayOfWeek', 'IsWeekend', 'ProductSubcategoryKey']\n",
    "\n",
    "# Create regression dataset\n",
    "df_reg = df_detailed[features_for_regression + ['SalesAmount']].copy()\n",
    "df_reg = df_reg.dropna()\n",
    "\n",
    "X_reg = df_reg[features_for_regression]\n",
    "y_reg = df_reg['SalesAmount']\n",
    "\n",
    "# Split data\n",
    "X_train_reg, X_test_reg, y_train_reg, y_test_reg = train_test_split(\n",
    "    X_reg, y_reg, test_size=0.2, random_state=42\n",
    ")\n",
    "\n",
    "# Train Random Forest Regressor\n",
    "rf_regressor = RandomForestRegressor(n_estimators=100, random_state=42)\n",
    "rf_regressor.fit(X_train_reg, y_train_reg)\n",
    "\n",
    "# Make predictions\n",
    "y_pred_reg = rf_regressor.predict(X_test_reg)\n",
    "\n",
    "# Evaluate regression model\n",
    "r2 = r2_score(y_test_reg, y_pred_reg)\n",
    "mae = mean_absolute_error(y_test_reg, y_pred_reg)\n",
    "rmse = np.sqrt(mean_squared_error(y_test_reg, y_pred_reg))\n",
    "mape = mean_absolute_percentage_error(y_test_reg, y_pred_reg)\n",
    "\n",
    "print(\"\\nSales Amount Regression Model Performance:\")\n",
    "print(f\"R² Score: {r2:.4f}\")\n",
    "print(f\"MAE: ${mae:,.2f}\")\n",
    "print(f\"RMSE: ${rmse:,.2f}\")\n",
    "print(f\"MAPE: {mape:.2%}\")\n",
    "\n",
    "# Feature importance for regression\n",
    "reg_feature_importance = pd.DataFrame({\n",
    "    'feature': features_for_regression,\n",
    "    'importance': rf_regressor.feature_importances_\n",
    "}).sort_values('importance', ascending=False)\n",
    "\n",
    "print(\"\\nFeature Importance for Sales Prediction:\")\n",
    "print(reg_feature_importance)\n",
    "\n",
    "# Plot actual vs predicted\n",
    "plt.figure(figsize=(12, 5))\n",
    "\n",
    "plt.subplot(1, 2, 1)\n",
    "plt.scatter(y_test_reg, y_pred_reg, alpha=0.5)\n",
    "plt.plot([y_test_reg.min(), y_test_reg.max()], [y_test_reg.min(), y_test_reg.max()], 'r--', lw=2)\n",
    "plt.xlabel('Actual Sales Amount')\n",
    "plt.ylabel('Predicted Sales Amount')\n",
    "plt.title('Actual vs Predicted Sales Amount')\n",
    "\n",
    "plt.subplot(1, 2, 2)\n",
    "sns.barplot(data=reg_feature_importance.head(8), x='importance', y='feature')\n",
    "plt.title('Top Feature Importance for Sales Prediction')\n",
    "plt.xlabel('Importance')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "model_comparison",
   "metadata": {},
   "source": [
    "## Model Comparison and Business Insights"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "business_insights",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Business Insights and Recommendations\n",
    "print(\"=\" * 60)\n",
    "print(\"ADVENTURE WORKS PREDICTION MODEL SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "print(\"\\n1. TIME SERIES FORECASTING:\")\n",
    "print(f\"   - Model: Prophet\")\n",
    "print(f\"   - Use Case: Monthly sales forecasting\")\n",
    "print(f\"   - Performance: Test RMSE = ${np.sqrt(mean_squared_error(test_ts['y'], test_pred)):,.0f}\")\n",
    "print(f\"   - Business Value: Budget planning, inventory management\")\n",
    "\n",
    "print(\"\\n2. CUSTOMER CLASSIFICATION:\")\n",
    "print(f\"   - Model: Random Forest Classifier\")\n",
    "print(f\"   - Use Case: Identify high-value customers\")\n",
    "print(f\"   - Threshold: ${threshold:,.0f} total sales\")\n",
    "print(f\"   - Business Value: Targeted marketing, customer retention\")\n",
    "\n",
    "print(\"\\n3. SALES AMOUNT PREDICTION:\")\n",
    "print(f\"   - Model: Random Forest Regressor\")\n",
    "print(f\"   - Use Case: Predict individual transaction amounts\")\n",
    "print(f\"   - Performance: R² = {r2:.3f}, MAPE = {mape:.1%}\")\n",
    "print(f\"   - Business Value: Pricing optimization, sales planning\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"KEY BUSINESS INSIGHTS:\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Top insights from the data\n",
    "print(f\"\\n• Total Revenue Analyzed: ${df_detailed['SalesAmount'].sum():,.0f}\")\n",
    "print(f\"• Number of Customers: {df_detailed['CustomerKey'].nunique():,}\")\n",
    "print(f\"• Number of Products: {df_detailed['ProductKey'].nunique():,}\")\n",
    "print(f\"• Average Order Value: ${df_detailed['SalesAmount'].mean():,.2f}\")\n",
    "print(f\"• High-Value Customers: {(customer_summary['HighValueCustomer'] == 1).sum():,} ({(customer_summary['HighValueCustomer'] == 1).mean():.1%})\")\n",
    "\n",
    "# Seasonal insights\n",
    "seasonal_sales = df_detailed.groupby('Quarter')['SalesAmount'].sum()\n",
    "best_quarter = seasonal_sales.idxmax()\n",
    "print(f\"\\n• Best Performing Quarter: Q{best_quarter} (${seasonal_sales[best_quarter]:,.0f})\")\n",
    "\n",
    "# Customer insights\n",
    "avg_age = df_detailed['CustomerAge'].mean()\n",
    "print(f\"• Average Customer Age: {avg_age:.1f} years\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"RECOMMENDATIONS:\")\n",
    "print(\"=\" * 60)\n",
    "print(\"\\n1. Focus marketing efforts on high-value customer segments\")\n",
    "print(\"2. Optimize inventory for seasonal patterns identified\")\n",
    "print(\"3. Use sales predictions for dynamic pricing strategies\")\n",
    "print(\"4. Implement customer retention programs for high-value customers\")\n",
    "print(\"5. Monitor model performance and retrain quarterly\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "future_predictions",
   "metadata": {},
   "source": [
    "## Future Predictions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "future_forecast",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Generate future predictions\n",
    "print(\"Generating Future Sales Forecasts...\")\n",
    "\n",
    "# Extend forecast for next 12 months\n",
    "future_periods = 12\n",
    "future_forecast = prophet_model.make_future_dataframe(periods=future_periods, freq='M')\n",
    "future_predictions = prophet_model.predict(future_forecast)\n",
    "\n",
    "# Get only future predictions\n",
    "future_only = future_predictions.tail(future_periods)[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]\n",
    "future_only['ds'] = future_only['ds'].dt.strftime('%Y-%m')\n",
    "\n",
    "print(\"\\nNext 12 Months Sales Forecast:\")\n",
    "print(future_only.to_string(index=False))\n",
    "\n",
    "# Calculate total predicted revenue for next year\n",
    "total_predicted = future_only['yhat'].sum()\n",
    "print(f\"\\nTotal Predicted Revenue (Next 12 Months): ${total_predicted:,.0f}\")\n",
    "\n",
    "# Plot future forecast\n",
    "plt.figure(figsize=(15, 8))\n",
    "plt.plot(df_monthly['ds'], df_monthly['y'], label='Historical Sales', linewidth=2)\n",
    "plt.plot(future_only['ds'], future_only['yhat'], label='Forecast', linewidth=2, linestyle='--')\n",
    "plt.fill_between(range(len(future_only)), future_only['yhat_lower'], future_only['yhat_upper'], alpha=0.3)\n",
    "plt.title('Adventure Works Sales Forecast - Next 12 Months')\n",
    "plt.xlabel('Date')\n",
    "plt.ylabel('Sales Amount')\n",
    "plt.legend()\n",
    "plt.xticks(rotation=45)\n",
    "plt.grid(True, alpha=0.3)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "print(\"\\nPrediction models completed successfully!\")\n",
    "print(\"Models are ready for deployment and can be used for:\")\n",
    "print(\"- Monthly sales forecasting\")\n",
    "print(\"- Customer segmentation and targeting\")\n",
    "print(\"- Individual transaction amount prediction\")\n",
    "print(\"- Business planning and decision making\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}